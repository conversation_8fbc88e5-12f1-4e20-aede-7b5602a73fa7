syntax = "proto3";

package common;

option java_package = "com.example.common";
option java_multiple_files = true;
option go_package = "github.com/example/bazel-example/proto/commonpb";

// Error codes
enum ErrorCode {
  ERROR_CODE_UNSPECIFIED = 0;
  ERROR_CODE_INVALID_ARGUMENT = 1;
  ERROR_CODE_NOT_FOUND = 2;
  ERROR_CODE_ALREADY_EXISTS = 3;
  ERROR_CODE_PERMISSION_DENIED = 4;
  ERROR_CODE_UNAUTHENTICATED = 5;
  ERROR_CODE_INTERNAL = 6;
  ERROR_CODE_UNAVAILABLE = 7;
  ERROR_CODE_UNIMPLEMENTED = 8;
}
