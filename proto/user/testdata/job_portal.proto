syntax = "proto3";

package user.testdata;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/example/bazel-example/proto/user/testdatapb";
option java_multiple_files = true;

service JobPortalService {
  rpc PostJob(PostJobRequest) returns (PostJobResponse);
  rpc ApplyForJob(ApplyForJobRequest) returns (ApplyForJobResponse);
  rpc GetJobApplications(GetJobApplicationsRequest) returns (stream JobApplication);
}

message Job {
  string job_id = 1;
  string title = 2;
  string description = 3;
  string company = 4;
  string location = 5;
}

message JobApplication {
  string application_id = 1;
  string job_id = 2;
  string user_id = 3;
  string status = 4;
  google.protobuf.Timestamp applied_at = 5;
}

message PostJobRequest {
  Job job = 1;
}

message PostJobResponse {
  Job job = 1;
  string error_message = 2;
}

message ApplyForJobRequest {
  string job_id = 1;
  string user_id = 2;
}

message ApplyForJobResponse {
  JobApplication application = 1;
  string error_message = 2;
}

message GetJobApplicationsRequest {
  string job_id = 1;
}
