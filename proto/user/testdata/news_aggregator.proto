syntax = "proto3";

package user.testdata;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/example/bazel-example/proto/user/testdatapb";
option java_multiple_files = true;

service NewsAggregatorService {
  rpc GetTopHeadlines(GetTopHeadlinesRequest) returns (GetTopHeadlinesResponse);
  rpc SearchArticles(SearchArticlesRequest) returns (stream Article);
  rpc GetArticle(GetArticleRequest) returns (Article);
}

message Article {
  string article_id = 1;
  string title = 2;
  string source = 3;
  string author = 4;
  string content = 5;
  google.protobuf.Timestamp published_at = 6;
}

message GetTopHeadlinesRequest {
  string country = 1;
  string category = 2;
}

message GetTopHeadlinesResponse {
  repeated Article articles = 1;
}

message SearchArticlesRequest {
  string query = 1;
}

message GetArticleRequest {
  string article_id = 1;
}
