load("@rules_go//go:def.bzl", "go_binary", "go_library", "go_test")

go_library(
    name = "lib",
    srcs = glob(["*.go"]),
    importpath = "github.com/example/bazel-example/services/notification/grpc",
    deps = [
        "//proto/common:proto_go",
        "//proto/user:proto_go",
        "//proto/notification:proto_go",
        "//proto/notification:grpc_go",

        "@org_golang_google_grpc//:go_default_library",
    ],
    visibility = ["//visibility:public"],
)
